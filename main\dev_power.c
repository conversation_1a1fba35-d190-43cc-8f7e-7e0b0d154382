#include "dev_power.h"
#include "esp_log.h"
#include "freertos/queue.h"

/* GPIO定义 */
#define DEV_POWER_BUTTON GPIO_NUM_0
#define DEV_DO_POWER GPIO_NUM_5

/* 按键检测参数 */
#define BUTTON_DEBOUNCE_TIME_MS 50     // 防抖时间 (毫秒)
#define BUTTON_LONG_PRESS_TIME_MS 2000 // 长按时间 (毫秒)
#define BUTTON_CHECK_INTERVAL_MS 50    // 按键检测间隔 (毫秒)

static const char *TAG = "DEV_POWER";
static bool power_initialized = false;
static TaskHandle_t power_button_task_handle = NULL;

/**
 * @brief 按键检测任务
 */
static void power_button_task(void *pvParameters)
{
    ESP_LOGI(TAG, "Power button monitoring task started");

    while (1)
    {
        // 检测按键是否按下 (按下为低电平)
        if (gpio_get_level(DEV_POWER_BUTTON) == 0)
        {
            // 按键按下，开始计时检测
            uint32_t press_count = 0;                                                     // 按下次数计数
            uint32_t total_checks = BUTTON_LONG_PRESS_TIME_MS / BUTTON_CHECK_INTERVAL_MS; // 总检测次数 (2000/50 = 40次)
            uint32_t required_press_count = (total_checks * 80) / 100;                    // 需要按下的次数 (80% = 32次)

            // 在2秒内每50ms检测一次按键状态
            for (uint32_t i = 0; i < total_checks; i++)
            {
                vTaskDelay(pdMS_TO_TICKS(BUTTON_CHECK_INTERVAL_MS));

                // 检测按键状态
                if (gpio_get_level(DEV_POWER_BUTTON) == 0)
                {
                    press_count++;
                }
            }

            // 按下时间长>=80%的时间，判定为长按, 执行关机
            if (press_count >= (press_count * 100) / total_checks)
            {
                gpio_set_level(DEV_DO_POWER, POWER_OFF);

                // 关机后等待一段时间
                vTaskDelay(pdMS_TO_TICKS(1000));
            }
        }

        // 检测间隔
        vTaskDelay(pdMS_TO_TICKS(BUTTON_CHECK_INTERVAL_MS));
    }
}

esp_err_t dev_power_init(void)
{
    if (power_initialized)
    {
        ESP_LOGW(TAG, "Power already initialized");
        return ESP_OK;
    }

    // 配置按键GPIO (输入模式，上拉)
    gpio_config_t power_button_io_conf = {
        .intr_type = GPIO_INTR_DISABLE, // 禁用中断
        .mode = GPIO_MODE_INPUT,        // 输入模式
        .pin_bit_mask = (1ULL << DEV_POWER_BUTTON),
        .pull_down_en = 0,
        .pull_up_en = GPIO_PULLUP_ENABLE,
    };

    // 配置电源控制GPIO (输出模式)
    gpio_config_t power_enable_io_conf = {
        .intr_type = GPIO_INTR_DISABLE, // 禁用中断
        .mode = GPIO_MODE_OUTPUT,       // 输出模式
        .pin_bit_mask = (1ULL << DEV_DO_POWER),
        .pull_down_en = 0,
        .pull_up_en = 0,
    };

    esp_err_t ret = gpio_config(&power_button_io_conf);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Power button GPIO config failed: %s", esp_err_to_name(ret));
        return ret;
    }

    ret = gpio_config(&power_enable_io_conf);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Power enable GPIO config failed: %s", esp_err_to_name(ret));
        return ret;
    }

    // 设置初始电源状态为开启
    ret = gpio_set_level(DEV_DO_POWER, POWER_ON);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to set initial power state: %s", esp_err_to_name(ret));
        return ret;
    }

    // 创建按键检测任务
    BaseType_t task_ret = xTaskCreate(
        power_button_task,        // 任务函数
        "power_button_task",      // 任务名称
        2048,                     // 堆栈大小
        NULL,                     // 任务参数
        5,                        // 任务优先级
        &power_button_task_handle // 任务句柄
    );

    if (task_ret != pdPASS)
    {
        ESP_LOGE(TAG, "Failed to create power button task");
        return ESP_FAIL;
    }

    power_initialized = true;
    ESP_LOGI(TAG, "Power control initialized - Button: GPIO%d, Power: GPIO%d", DEV_POWER_BUTTON, DEV_DO_POWER);

    return ESP_OK;
}
